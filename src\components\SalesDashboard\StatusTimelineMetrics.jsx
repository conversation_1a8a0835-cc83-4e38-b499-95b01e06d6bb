import { Table, Tag } from 'antd';
import { useState } from 'react';
import { useGetQuotationStatusesQuery } from "../../slices/quotationApiSlice";
import Pagination from '../global/components/Pagination';
const dummyQuotations = [
  {
    key: '1',
    quoteId: '#12345', 
    currentStatus: 'pending',
    companyName: 'ABC Inc.',
    statusTimeline: {
      'pending': '2024-06-01',
      'Hold': null,
      'Processing': null,
      'approved': null,
      'rejected': null,
      'completed': null
    }
  },
  {
    key: '2',
    quoteId: '#12346',
    currentStatus: 'Hold',
    companyName: 'XYZ Ltd.',
    statusTimeline: {
      'pending': '2024-06-01',
      'Hold': '2024-06-02',
      'Processing': null,
      'approved': null, 
      'rejected': null,
      'completed': null
    }
  },
  {
    key: '3',
    quoteId: '#12347',
    currentStatus: 'Processing',
    companyName: 'LMN Corp.',
    statusTimeline: {
      'pending': '2024-06-01',
      'Hold': '2024-06-02',
      'Processing': '2024-06-03',
      'approved': null,
      'rejected': null,
      'completed': null
    }
  },
  {
    key: '4',
    quoteId: '#12348',
    currentStatus: 'approved',
    companyName: 'PQR Pvt. Ltd.',
    statusTimeline: {
      'pending': '2024-06-01',
      'Hold': '2024-06-02',
      'Processing': '2024-06-03',
      'approved': '2024-06-04',
      'rejected': null,
      'completed': null
    }
  },
  {
    key: '5',
    quoteId: '#12349',
    currentStatus: 'completed',
    companyName: 'STU LLC',
    statusTimeline: {
      'pending': '2024-06-01',
      'Hold': '2024-06-02',
      'Processing': '2024-06-03',
      'approved': '2024-06-04',
      'rejected': null,
      'completed': '2024-06-05'
    }
  },
  {
    key: '6',
    quoteId: '#12350',
    currentStatus: 'rejected',
    companyName: 'VWX Ltd.',
    statusTimeline: {
      'pending': '2024-06-01',
      'Hold': '2024-06-02',
      'Processing': '2024-06-03',
      'approved': null,
      'rejected': '2024-06-04',
      'completed': null
    }
  }
];
const defaultStatuses = ['pending', 'approved', 'rejected', 'completed'];
const StatusTimelineMetrics = () => {
    const [page, setPage] = useState(1);
    const [limit, setLimit] = useState(10);
  const { data: statuses, isLoading: statusesLoading, error: statusesError } = useGetQuotationStatusesQuery();
  const availableStatuses = statuses?.status 
    ? [...statuses.status, ...defaultStatuses]
    : [...defaultStatuses];

  const quotationsData = dummyQuotations; 

  // Build dynamic columns based on available statuses
  const buildColumns = () => {
    const columns = [
      {
        title: 'Quote ID',
        dataIndex: 'quoteId',
        key: 'quoteId',
        width: 120,
        fixed: 'left',
        render: (text) => (
          <span style={{ color: '#1890ff', fontWeight: 500 }}>{text}</span>
        )
      },
      {
        title: 'Company',
        dataIndex: 'companyName',
        key: 'companyName',
        width: 120,
        fixed: 'left',
        render: (text) => (
          <span style={{ color: '#1890ff', fontWeight: 500 }}>{text}</span>
        )
      },
      {
        title: 'Status',
        dataIndex: 'currentStatus',
        key: 'currentStatus',
        width: 120,
        fixed: 'left',
        render: (status) => (
          <Tag>
            {status}
          </Tag>
        )
      }
    ];

    availableStatuses.forEach(status => {
      columns.push({
        title: status,
        dataIndex: ['statusTimeline', status],
        key: status,
        width: 130,
        render: (date, record) => {
          const statusDate = record.statusTimeline[status];
          if (!statusDate) {
            return <span style={{ color: '#d9d9d9' }}>-</span>;
          }
          return (
            <span style={{ 
              color: '#1890ff',
              fontSize: '13px'
            }}>
              {statusDate}
            </span>
          );
        }
      });
    });

    return columns;
  };

  if (statusesLoading) {
    return <Table loading={true} />;
  }

  if (statusesError) {
    return (
      <div style={{ 
        textAlign: 'center', 
        padding: '48px',
        color: '#ff4d4f'
      }}>
        Error loading statuses
      </div>
    );
  }

  return (
    <div>
      <Table
        columns={buildColumns()}
        dataSource={quotationsData}
        pagination={false}
        scroll={{ x: 'max-content' }}
        size="middle"
        bordered
      />
      <Pagination
        limit={limit}
        page={page}
        totalPages={quotationsData?.totalPages}
        totalResults={quotationsData?.totalResults}
        setPage={setPage}
        setLimit={setLimit}
        className="w-full"
      />
    </div>
  );
};

export default StatusTimelineMetrics;
