import { <PERSON><PERSON>, Button, Empty, Spin } from 'antd';
import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';
import { unCamelCaseString } from '../../helperFunction';
import BentoGrid from '../global/components/BentoGrid';
import AreaChart from './charts/AreaChart';
import LineChart from './charts/LineChart';
import PieChart from './charts/PieChart';
import UserStatsLineChart from './charts/UserStatsLineChart';
import MetricsCards from './MetricsCards';
import QuotationFormatsTable from './QuotationFormatsTable';
import StatusTimelineMetrics from './StatusTimelineMetrics';
import TopCustomers from './TopCustomers';
import TopProducts from './TopProducts';

const QuotationsTab = ({
  dateRange,
  selectedCustomer,
  dashboardData,
  topCustomerFilter,
  setTopCustomerFilter,
}) => {
  const [isMobile, setIsMobile] = useState(false);

  const {
    metrics: filteredMetrics = {},
    trendData: filteredTrendData = [],
    statusData: filteredStatusData = [],
    conversionData = [],
    topCustomers = [],
    topProducts = [],
    formats: quotationFormats = [],
    isLoadingMetrics = false,
    isLoadingTrend = false,
    isLoadingTopCustomers = false,
    isLoadingTopProducts = false,
    isLoadingFormats = false,
    isLoadingConversion = false,
    isLoadingUserStats = false,
    metricsError,
    trendError,
    topCustomersError,
    topProductsError,
    formatsError,
    conversionError,
    userStatsError,
    hasData = false,
    selectedMetrics = [],
    updateSelectedMetrics,
    userStats = [],
  } = dashboardData || {};

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  // Use hasData from dashboard hook or fallback logic
  const dataAvailable =
    hasData ||
    (filteredTrendData.length > 0 && filteredMetrics?.totalQuotations > 0);

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      <MetricsCards
        metrics={filteredMetrics}
        dateRange={dateRange}
        isLoading={isLoadingMetrics}
        type="quotations"
        selectedMetrics={selectedMetrics}
        onSelectedMetricsChange={updateSelectedMetrics}
        selectedCustomer={selectedCustomer}
      />

      {metricsError && !isLoadingMetrics && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center py-16"
        >
          <Alert
            message="Error Loading Data"
            description="There was an error loading the quotations data. Please try again."
            type="error"
            showIcon
            action={
              <button
                onClick={() => dashboardData?.refetch?.metrics?.()}
                className="text-red-600 hover:text-red-800 font-medium"
              >
                Retry
              </button>
            }
          />
        </motion.div>
      )}

      {!dataAvailable &&
        !isLoadingMetrics &&
        !metricsError &&
        !isLoadingTrend &&
        !trendError && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center py-16"
          >
            <Empty
              description={
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No data available
                  </h3>
                  <p className="text-gray-600">
                    {(dateRange && dateRange[0] && dateRange[1]) ||
                    selectedCustomer
                      ? `No quotations found for the selected ${dateRange && dateRange[0] && dateRange[1] ? 'date range' : ''}${dateRange && dateRange[0] && dateRange[1] && selectedCustomer ? ' and ' : ''}${selectedCustomer ? 'customer' : ''}`
                      : 'No quotation data available'}
                  </p>
                </div>
              }
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </motion.div>
        )}

      {(dataAvailable ||
        Object.values({
          isLoadingMetrics,
          isLoadingTrend,
          isLoadingTopCustomers,
          isLoadingTopProducts,
          isLoadingFormats,
          isLoadingConversion,
        }).some(Boolean)) &&
        !(metricsError && trendError) && (
          <BentoGrid className="quotations-grid">
            <BentoGrid.Item
              span={2}
              title="Quotations Trend"
              subtitle="Monthly quotation performance"
              index={0}
            >
              {isLoadingTrend ? (
                <div className="flex items-center justify-center h-64">
                  <Spin size="large" />
                </div>
              ) : trendError ? (
                <div className="flex items-center justify-center h-64">
                  <Alert
                    message="Error Loading Trend Data"
                    description="Failed to load quotations trend. Please try again."
                    type="error"
                    showIcon
                    action={
                      <Button
                        onClick={() => dashboardData?.refetch?.trend?.()}
                        type="text"
                        className="text-red-600 hover:text-red-800"
                      >
                        Retry
                      </Button>
                    }
                  />
                </div>
              ) : (
                <LineChart
                  data={filteredTrendData}
                  height={isMobile ? 240 : 280}
                  showLegend={true}
                  type="quotations"
                />
              )}
            </BentoGrid.Item>

            <BentoGrid.Item
              span={1}
              title="Status Distribution"
              subtitle="Current quotation status"
              index={1}
            >
              {isLoadingMetrics ? (
                <div className="flex items-center justify-center h-64">
                  <Spin size="large" />
                </div>
              ) : metricsError ? (
                <div className="flex items-center justify-center h-64">
                  <Alert
                    message="Error Loading Status Data"
                    description="Failed to load status distribution. Please try again."
                    type="error"
                    showIcon
                    action={
                      <Button
                        onClick={() => dashboardData?.refetch?.metrics?.()}
                        type="text"
                        className="text-red-600 hover:text-red-800"
                      >
                        Retry
                      </Button>
                    }
                  />
                </div>
              ) : (
                <PieChart
                  data={filteredStatusData}
                  height={isMobile ? 240 : 280}
                  doughnut={true}
                  showLegend={true}
                  variant={isMobile ? 'compact' : 'default'}
                  centerText={{
                    value: (filteredMetrics?.totalQuotations || 0).toString(),
                    label: 'Total',
                  }}
                />
              )}
            </BentoGrid.Item>
            <BentoGrid.Item
              span={1}
              title="Quotation → Sales Order Conversion"
              subtitle="Monthly conversion tracking"
              index={2}
            >
              {isLoadingConversion ? (
                <div className="flex items-center justify-center h-64">
                  <Spin size="large" />
                </div>
              ) : conversionError ? (
                <div className="flex items-center justify-center h-64">
                  <Alert
                    message="Error Loading Conversion Data"
                    description="Failed to load conversion data. Please try again."
                    type="error"
                    showIcon
                    action={
                      <Button
                        onClick={() => dashboardData?.refetch?.conversion?.()}
                        type="text"
                        className="text-red-600 hover:text-red-800"
                      >
                        Retry
                      </Button>
                    }
                  />
                </div>
              ) : conversionData && conversionData.length > 0 ? (
                <AreaChart
                  data={conversionData}
                  height={isMobile ? 240 : 280}
                  showLegend={true}
                  colors={['#059669', '#dc2626']}
                />
              ) : (
                <div className="flex items-center justify-center h-64">
                  <Empty
                    description="No conversion data available"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                </div>
              )}
            </BentoGrid.Item>

            <BentoGrid.Item
              span={1}
              index={3}
              title="Top Customers"
              subtitle={`Best customers by ${unCamelCaseString(topCustomerFilter.by)} ${topCustomerFilter.order === 'desc' ? 'Ascending' : 'Descending'}`}
            >
              {isLoadingTopCustomers ? (
                <div className="flex items-center justify-center h-64">
                  <Spin size="large" />
                </div>
              ) : topCustomersError ? (
                <div className="flex items-center justify-center h-64">
                  <Alert
                    message="Error Loading Top Customers"
                    description="Failed to load top customers data. Please try again."
                    type="error"
                    showIcon
                    action={
                      <Button
                        onClick={() => dashboardData?.refetch?.topCustomers?.()}
                        type="text"
                        className="text-red-600 hover:text-red-800"
                      >
                        Retry
                      </Button>
                    }
                  />
                </div>
              ) : (
                <TopCustomers
                  data={topCustomers}
                  topCustomerFilter={topCustomerFilter}
                  setTopCustomerFilter={setTopCustomerFilter}
                />
              )}
            </BentoGrid.Item>

            <BentoGrid.Item
              span={1}
              index={4}
              title="Top 5 Products"
              subtitle="Best products by total Quotations"
            >
              {isLoadingTopProducts ? (
                <div className="flex items-center justify-center h-64">
                  <Spin size="large" />
                </div>
              ) : topProductsError ? (
                <div className="flex items-center justify-center h-64">
                  <Alert
                    message="Error Loading Top Products"
                    description="Failed to load top products data. Please try again."
                    type="error"
                    showIcon
                    action={
                      <Button
                        onClick={() => dashboardData?.refetch?.topProducts?.()}
                        type="text"
                        className="text-red-600 hover:text-red-800"
                      >
                        Retry
                      </Button>
                    }
                  />
                </div>
              ) : (
                <TopProducts data={topProducts} isLoading={false} />
              )}
            </BentoGrid.Item>

            <BentoGrid.Item
              span={2}
              index={5}
              title={`${quotationFormats.length ? 'Quotation Formats' : 'Customize This Section'}`}
              subtitle={`${quotationFormats.length ? 'Available quotation formats' : 'You can customize this section based on your requirements'}`}
            >
              {isLoadingFormats ? (
                <div className="flex items-center justify-center h-64">
                  <Spin size="large" />
                </div>
              ) : formatsError ? (
                <div className="flex items-center justify-center h-64">
                  <Alert
                    message="Error Loading Formats"
                    description="Failed to load quotation formats. Please try again."
                    type="error"
                    showIcon
                    action={
                      <Button
                        onClick={() => dashboardData?.refetch?.formats?.()}
                        type="text"
                        className="text-red-600 hover:text-red-800"
                      >
                        Retry
                      </Button>
                    }
                  />
                </div>
              ) : quotationFormats && quotationFormats.length > 0 ? (
                <QuotationFormatsTable
                  data={quotationFormats}
                  isLoading={false}
                  dateRange={dateRange}
                  selectedCustomer={selectedCustomer}
                />
              ) : (
                <div className="flex flex-col items-center justify-center p-8 bg-gray-50 rounded-lg">
                  <Empty
                    description={
                      <div className="text-center">
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          No Data Available
                        </h3>
                        <p className="text-gray-600 mb-4">
                          You can customize this section based on your
                          requirements.
                        </p>
                        <p className="text-blue-600 hover:text-blue-800">
                          Contact us to customize
                        </p>
                      </div>
                    }
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                </div>
              )}
            </BentoGrid.Item>
            <BentoGrid.Item
              span={4}
              index={6}
              title="Employee Performance"
              subtitle="Employee performance over time"
            >
              {isLoadingUserStats ? (
                <div className="flex items-center justify-center h-64">
                  <Spin size="large" />
                </div>
              ) : userStatsError ? (
                <div className="flex items-center justify-center h-64">
                  <Alert
                    message="Error Loading Top Products"
                    description="Failed to load top products data. Please try again."
                    type="error"
                    showIcon
                    action={
                      <Button
                        onClick={() => dashboardData?.refetch?.userStats?.()}
                        type="text"
                        className="text-red-600 hover:text-red-800"
                      >
                        Retry
                      </Button>
                    }
                  />
                </div>
              ) : (
                <UserStatsLineChart
                  data={userStats}
                  isLoading={false}
                  height={isMobile ? 240 : 280}
                />
              )}
          </BentoGrid.Item>
          <BentoGrid.Item
            span={4}
            index={7}
            title="Status Timeline"
            subtitle="Status timeline of all quotations"
          >
            <StatusTimelineMetrics />
          </BentoGrid.Item>
          </BentoGrid>
        )}
    </motion.div>
  );
};

export default QuotationsTab;
